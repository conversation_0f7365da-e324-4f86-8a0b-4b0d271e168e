<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Custom Invoice Report Template with Product Type Sections -->
        <template id="report_invoice_document_cigarette_tobacco" inherit_id="account.report_invoice_document">
            <!-- Replace table headers to match existing structure -->
            <xpath expr="//table[@name='invoice_line_table']/thead" position="replace">
                <thead>
                    <tr>
                        <t t-set="colspan" t-value="8"/>
                        <th name="th_no" class="text-left"><span>No.</span></th>
                        <th name="th_sku" class="text-left"><span>SKU</span></th>
                        <th name="th_description" class="text-left"><span>Description</span></th>
                        <th name="th_quantity" class="text-right"><span>Quantity</span></th>
                        <th name="th_volume" class="text-right"><span>Vol (ml)</span></th>
                        <th name="th_total_volume" class="text-right"><span>Total Vol (ml)</span></th>
                        <th name="th_priceunit" class="text-right"><span>Price</span></th>
                        <th name="th_subtotal" class="text-right">
                            <span groups="account.group_show_line_subtotals_tax_excluded">Amount</span>
                            <span groups="account.group_show_line_subtotals_tax_included">Amount</span>
                        </th>
                    </tr>
                </thead>
            </xpath>

            <xpath expr="//table[@name='invoice_line_table']/tbody" position="replace">
                <tbody class="invoice_tbody">
                    <t t-set="current_subtotal" t-value="0"/>
                    <t t-set="lines_by_type" t-value="o._get_invoice_lines_by_type()"/>
                    <t t-set="sr_no" t-value="1"/>

                    <t t-foreach="lines_by_type" t-as="section">
                        <!-- Section Header with better styling -->
                        <tr style="border-top: 2px solid #000; border-bottom: 1px solid #000; background-color: #f8f9fa;">
                            <td colspan="8" style="padding: 8px; font-weight: bold; font-size: 14px; text-transform: uppercase;">
                                <strong t-esc="section['name']"/>
                                <span style="font-size: 12px; font-weight: normal; margin-left: 10px;">
                                    (<t t-esc="section['count']"/> items)
                                </span>
                            </td>
                        </tr>

                        <!-- Section Lines -->
                        <t t-foreach="section['lines']" t-as="line">
                            <t t-if="line.product_id.name not in ['Damage &amp; Returns','Product Discount']">
                                <t t-set="current_subtotal" t-value="current_subtotal + (line.price_unit * line.quantity)" groups="account.group_show_line_subtotals_tax_excluded"/>
                                <t t-set="current_subtotal" t-value="current_subtotal + (line.price_unit * line.quantity)" groups="account.group_show_line_subtotals_tax_included"/>

                                <tr t-att-class="'bg-200 font-weight-bold o_line_section' if line.display_type == 'line_section' else 'font-italic o_line_note' if line.display_type == 'line_note' else ''">
                                    <t t-if="not line.display_type" name="account_invoice_line_accountable">
                                        <td t-esc="sr_no"></td>
                                        <t t-set="sr_no" t-value="sr_no+1"/>
                                        <td class="text-left">
                                            <span t-field="line.product_id.barcode" t-if="line.product_id.barcode"/>
                                            <span t-if="not line.product_id.barcode" t-esc="line.product_id.default_code or ''"/>
                                        </td>
                                        <td name="account_invoice_line_name">
                                            <span t-field="line.name" t-options="{'widget': 'text'}"/>
                                        </td>
                                        <td class="text-right">
                                            <span t-field="line.quantity"/>
                                            <span t-field="line.product_uom_id" groups="uom.group_uom"/>
                                        </td>
                                        <td class="text-right">
                                            <!-- Use existing volume field or x_volume -->
                                            <span t-field="line.product_id.volume" t-if="line.product_id.volume"/>
                                            <span t-field="line.product_id.x_volume" t-if="not line.product_id.volume and line.product_id.x_volume"/>
                                            <span t-if="not line.product_id.volume and not line.product_id.x_volume">0.0</span>
                                        </td>
                                        <td class="text-right">
                                            <!-- Use existing x_total_volume field or calculate -->
                                            <span t-field="line.x_total_volume" t-if="line.x_total_volume"/>
                                            <span t-if="not line.x_total_volume" t-esc="(line.quantity * ((line.product_id.volume or line.product_id.x_volume) or 0))"/>
                                        </td>
                                        <td class="text-right">
                                            <span class="text-nowrap" t-field="line.price_unit"/>
                                        </td>
                                        <td class="text-right o_price_total">
                                            <span class="text-nowrap" t-esc="'%.2f' % (line.quantity * line.price_unit)" groups="account.group_show_line_subtotals_tax_excluded"/>
                                            <span class="text-nowrap" t-esc="'%.2f' % (line.quantity * line.price_unit)" groups="account.group_show_line_subtotals_tax_included"/>
                                        </td>
                                    </t>
                                    <t t-if="line.display_type == 'line_section'">
                                        <td colspan="8">
                                            <span t-field="line.name" t-options="{'widget': 'text'}"/>
                                        </td>
                                    </t>
                                    <t t-if="line.display_type == 'line_note'">
                                        <td colspan="8">
                                            <span t-field="line.name" t-options="{'widget': 'text'}"/>
                                        </td>
                                    </t>
                                </tr>
                            </t>
                        </t>

                        <!-- Section Subtotal with better styling -->
                        <t t-if="section['lines']">
                            <tr style="border-top: 1px solid #000; border-bottom: 2px solid #000; background-color: #e9ecef;">
                                <td colspan="7" style="padding: 6px 8px; font-weight: bold; text-align: right; font-size: 13px;">
                                    <strong>
                                        <span t-esc="section['name']"/> SUBTOTAL:
                                    </strong>
                                </td>
                                <td style="padding: 6px 8px; font-weight: bold; text-align: right; border-left: 1px solid #000; font-size: 13px;">
                                    <strong>
                                        <span t-esc="section['subtotal']" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                    </strong>
                                </td>
                            </tr>
                            <!-- Add spacing between sections -->
                            <tr style="height: 15px;">
                                <td colspan="8" style="border: none; background-color: white;"></td>
                            </tr>
                        </t>
                    </t>
                </tbody>
            </xpath>
        </template>
    </data>
</odoo>
